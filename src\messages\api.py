from fastapi import APIRouter, Request, HTTPException, status, Depends
from src.auth.api import get_current_user
from src.users.models import UserRecordModel
from math import ceil
from src.lib.constants import PAGE_SIZE
from src.messages.models import (
    MessageCreateModel,
    # MessageRecordModel,
    FeedMessageApiReturnModel,
)
from src.messages.functions import create_full_message_record
from src.db.functions import json_serial
import json

# from src.messages.functions import create_full_message_record
#
messages = APIRouter()


@messages.get("s", response_description="List all messages")
async def list_all_messages(
    request: Request,  # min_price: int = 0,
    # max_price: int = 100000,
    # brand: Optional[str] = None,
    page: int = 1,
    page_size: int = PAGE_SIZE,
):
    skip = (page - 1) * page_size

    # query = {"price": {"$lt": max_price, "$gt": min_price}}
    query = {}
    # if brand:
    #     query["brand"] = brand

    # count total docs
    pages = ceil(
        # await request.app.mongodb["users"].count_documents(query) /
        # RESULTS_PER_PAGE
        await request.app.mongodb["messages"].count_documents(query)
        / page_size  # noqa
    )

    full_query = (
        request.app.mongodb["messages"]
        .find(query)
        .sort("date_created")
        .skip(skip)
        .limit(page_size)
    )

    # Convert MongoDB documents to dictionaries first
    results = []
    async for raw_msg in full_query:
        # Convert ObjectId to string if present
        if "_id" in raw_msg:
            raw_msg["_id"] = str(raw_msg["_id"])

        # Skip model conversion and directly use the document
        # Just ensure all fields are JSON serializable
        serializable_msg = json.loads(json.dumps(raw_msg, default=json_serial))
        results.append(serializable_msg)

    return {"results": results, "pages": pages}


@messages.post("", response_description="Create a new message")
async def create_message(
    request: Request,
    message: MessageCreateModel,
    user: UserRecordModel = Depends(get_current_user),
):
    user_count = await request.app.mongodb["users"].count_documents({"id": user.id})
    # user not found check
    if user_count < 1:
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="User not found. " "  Unable to create message.",
        )
    # empty message check
    if len(message.content) < 1:
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="Empty message content;" " unable to create message.",
        )
    new_msg_record = -1
    try:
        full_message = create_full_message_record(message)
        new_msg_record = await request.app.mongodb["messages"].insert_one(  # noqa
            dict(full_message)
        )
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    retval = full_message["id"]
    return {"results": {"id": retval}}


# TODO: NEED TO ENFORCE AUTHORIZATION ON THIS ONE
@messages.delete("", response_description="Delete a message")
async def delete_message(
    request: Request, message_id: str, user: UserRecordModel = Depends(get_current_user)
):
    message_count = await request.app.mongodb["messages"].count_documents(
        {"id": message_id}
    )
    # user not found check
    if message_count < 1:
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="Message not found. " "  Unable to delete message.",
        )
    try:
        result = await request.app.mongodb["messages"].delete_one({"id": message_id})
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    if result.deleted_count == 0:
        raise HTTPException(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="No message was deleted.  Internal error.",
        )
    return {"results": {"status": result.acknowledged}}


@messages.get("s/feed", response_description="List feed messages")
async def list_feed_messages(
    request: Request,
    page: int = 1,
    page_size: int = PAGE_SIZE,
):
    skip = (page - 1) * page_size

    query = {"parent_id": ""}

    # count total records
    pages = ceil(
        # RESULTS_PER_PAGE
        await request.app.mongodb["messages"].count_documents(query)
        / page_size  # noqa
    )
    pipeline = [
        # Match messages with empty parent_id
        {"$match": {"parent_id": ""}},
        # Sort by date_created
        {"$sort": {"date_created": 1}},
        # Skip for pagination
        {"$skip": skip},
        # Limit results
        {"$limit": page_size},
        # Join with users collection
        {
            "$lookup": {
                "from": "users",
                "localField": "user_id",
                "foreignField": "id",
                "as": "user",
            }
        },
        # Unwind the user array
        {"$unwind": "$user"},
        # Add username and avatar_url fields
        {
            "$addFields": {
                "username": "$user.username",
                "user_avatar_url": "$user.avatar_link",
            }
        },
        # Look up reactions for this message
        {
            "$lookup": {
                "from": "reactions",
                "localField": "id",
                "foreignField": "target_id",
                "as": "all_reactions",
            }
        },
        # Group reactions by type and count into a single object
        {
            "$addFields": {
                "reactions_array": {
                    "$map": {
                        "input": {"$setUnion": ["$all_reactions.reaction"]},
                        "as": "reaction_type",
                        "in": {
                            "k": "$$reaction_type",
                            "v": {
                                "$size": {
                                    "$filter": {
                                        "input": "$all_reactions",
                                        "as": "r",
                                        "cond": {
                                            "$eq": ["$$r.reaction", "$$reaction_type"]
                                        },
                                    }
                                }
                            },
                        },
                    }
                }
            }
        },
        # Convert array to object using $arrayToObject
        {"$addFields": {"reactions": {"$arrayToObject": "$reactions_array"}}},
        # Remove temporary array and other fields
        {"$project": {"user": 0, "all_reactions": 0, "reactions_array": 0}},
    ]
    full_query = (
        request.app.mongodb["messages"].aggregate(pipeline)
        # .find(query)
        # .sort("date_created")
        # .skip(skip)
        # .limit(page_size)
    )

    # Convert MongoDB documents to FeedMessageApiReturnModel
    results = []
    async for raw_msg in full_query:
        # Convert ObjectId to string if present
        raw_msg["num_replies"] = len(raw_msg["replies"])
        if "_id" in raw_msg:
            raw_msg["_id"] = str(raw_msg["_id"])

        # No need to transform reactions - already in correct format
        # Convert raw document to FeedMessageApiReturnModel
        feed_message = FeedMessageApiReturnModel(**raw_msg)
        results.append(feed_message)

    return {"results": results, "pages": pages}
