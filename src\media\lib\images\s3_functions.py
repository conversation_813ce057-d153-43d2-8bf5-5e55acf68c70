from uuid import uuid4
from fastapi import HTTPException, UploadFile
from botocore.exceptions import NoCredentialsError, ClientError
from src.globals import AWS_S3_BUCKET_NAME, AWS_S3_REGION
from src.users.models import UserRecordModel
from fastapi.responses import JSONResponse, StreamingResponse


def get_user_img_dir(user: UserRecordModel):
    return f"users/{user.id}/img/"


def get_s3_image_from_key(s3_client, image_key: str):
    try:
        # Get the image object from S3
        response = s3_client.get_object(Bucket=AWS_S3_BUCKET_NAME, Key=image_key)

        # Determine content type based on file extension
        content_type = "image/jpeg"  # default
        if image_key.lower().endswith((".png", ".PNG")):
            content_type = "image/png"
        elif image_key.lower().endswith((".gif", ".GIF")):
            content_type = "image/gif"
        elif image_key.lower().endswith((".webp", ".WEBP")):
            content_type = "image/webp"

        # Return the image as a streaming response
        return StreamingResponse(
            response["Body"],
            media_type=content_type,
            headers={
                "Content-Disposition": f"inline; filename={image_key.split('/')[-1]}",
                "Cache-Control": "public, max-age=3600",
            },
        )

    except ClientError as e:
        error_code = e.response["Error"]["Code"]
        if error_code == "NoSuchKey":
            raise HTTPException(status_code=404, detail="Image not found.")
        else:
            raise HTTPException(
                status_code=500, detail=f"Failed to retrieve image: {str(e)}."
            )
    except NoCredentialsError:
        raise HTTPException(status_code=403, detail="Credentials not available.")


def upload_image_to_s3(s3_client, file: UploadFile, img_dir: str):
    if not file.filename or len(file.filename) == 0:
        file.filename = str(uuid4())
    try:
        # Upload the file to S3
        s3_client.upload_fileobj(
            file.file,
            # File object
            AWS_S3_BUCKET_NAME,
            # Bucket name
            img_dir + file.filename,
            # File name in S3
            ExtraArgs={"ACL": "public-read"},
            # Make file public
        )

        # Get the URL of the uploaded file
        file_url = (
            f"https://{AWS_S3_BUCKET_NAME}.s3."
            f"{AWS_S3_REGION}.amazonaws.com/{file.filename}"
        )
        # TODO: REMOVE THIS WHEN GOING LIVE!  USERS DO NOT NEED TO SEE THE FILE URL IN THE RESPONSE! SECURITY
        return JSONResponse(status_code=200, content={"file_url": file_url})
    except NoCredentialsError:
        raise HTTPException(status_code=403, detail="Credentials not available.")
    except ClientError as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to upload image: {str(e)}."
        )


def edit_s3_image(s3_client, file: UploadFile, image_key):
    s3_client.upload_fileobj(file.file, AWS_S3_BUCKET_NAME, image_key)
    # return {"message": "Image edited successfully"}


def delete_s3_image(s3_client, image_key: str):
    s3_client.delete_object(Bucket=AWS_S3_BUCKET_NAME, Key=image_key)


def upload_multiple_images_to_s3(s3_client, files: list[UploadFile], img_dir: str):
    """
    Upload multiple files to S3 in a single request.

    Args:
        s3_client: Boto3 S3 client
        files: List of UploadFile objects to upload
        img_dir: Directory path where images should be stored

    Returns:
        JSONResponse with upload results and file URLs
    """
    if not files or len(files) == 0:
        raise HTTPException(status_code=400, detail="No files provided.")

    uploaded_files = []
    failed_uploads = []

    try:
        for file in files:
            try:
                # Generate filename if missing
                if not file.filename or len(file.filename) == 0:
                    file.filename = str(uuid4())

                # Create unique filename to avoid conflicts
                unique_filename = f"{str(uuid4())}_{file.filename}"
                file_path = img_dir + unique_filename

                # Upload the file to S3
                s3_client.upload_fileobj(
                    file.file,
                    AWS_S3_BUCKET_NAME,
                    file_path,
                    ExtraArgs={"ACL": "public-read"},
                )

                uploaded_files.append(file_path)

            except Exception as e:
                failed_uploads.append(
                    {"filename": file.filename or "unknown", "error": str(e)}
                )

        # Determine status code based on results
        if len(uploaded_files) == 0:
            raise HTTPException(status_code=500, detail="All file uploads failed.")
        elif len(failed_uploads) > 0:
            # Partial success - some files uploaded, some failed
            response_data = {
                "uploaded_count": len(uploaded_files),
                "failed_count": len(failed_uploads),
                "uploaded_files": uploaded_files,
                "failed_uploads": failed_uploads,
            }
            return JSONResponse(status_code=207, content=response_data)  # Multi-Status
        else:
            # All files uploaded successfully - return only the file keys
            return uploaded_files

    except NoCredentialsError:
        raise HTTPException(status_code=403, detail="Credentials not available.")
    except ClientError as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to upload images: {str(e)}."
        )


def delete_multiple_images_from_s3(s3_client, image_keys: list[str]):
    """
    Delete multiple images from S3 in a single batch request.

    Args:
        s3_client: Boto3 S3 client
        image_keys: List of S3 keys (file paths) to delete

    Returns:
        List of successfully deleted keys or raises HTTPException
    """
    if not image_keys or len(image_keys) == 0:
        raise HTTPException(status_code=400, detail="No image keys provided.")

    try:
        # Prepare objects for batch deletion
        objects_to_delete = [{"Key": key} for key in image_keys]

        # Perform batch deletion
        response = s3_client.delete_objects(
            Bucket=AWS_S3_BUCKET_NAME,
            Delete={
                "Objects": objects_to_delete,
                "Quiet": False,  # Return information about deleted objects
            },
        )

        # Extract successfully deleted keys
        deleted_keys = []
        if "Deleted" in response:
            deleted_keys = [obj["Key"] for obj in response["Deleted"]]

        # Check for errors
        failed_deletions = []
        if "Errors" in response:
            failed_deletions = [
                {
                    "key": error["Key"],
                    "code": error["Code"],
                    "message": error["Message"],
                }
                for error in response["Errors"]
            ]

        # Handle results
        if len(deleted_keys) == 0:
            raise HTTPException(status_code=500, detail="All image deletions failed.")
        elif len(failed_deletions) > 0:
            # Partial success - some deleted, some failed
            return JSONResponse(
                status_code=207,  # Multi-Status
                content={
                    "deleted_count": len(deleted_keys),
                    "failed_count": len(failed_deletions),
                    "deleted_keys": deleted_keys,
                    "failed_deletions": failed_deletions,
                },
            )
        else:
            # All deletions successful - return only the deleted keys
            return deleted_keys

    except NoCredentialsError:
        raise HTTPException(status_code=403, detail="Credentials not available.")
    except ClientError as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to delete images: {str(e)}."
        )
