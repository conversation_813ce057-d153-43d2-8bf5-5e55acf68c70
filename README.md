# reefdojo-be

## Status

![Lin<PERSON> (flake8)](https://github.com/dsaves/reefdojo-be/actions/workflows/linter.yml/badge.svg?branch=master)

## Python Virtual ENV manager: pipenv

YOU WILL HAVE TO SET AN ENVIRONMENT VARIABLE TO RUN THE DEV SERVER PROPERLY:
`REEFDOJO-BE-ENV=DEV`

You want to run `pipenv install requirements.txt` upon cloning the repo.

Or just run `pip install -r requirements.txt` once you have a virtual environment going in PyCharm

## To get working

1. You will have to **add your IP address to the MongoDB whitelist** to get anything working, seriously, don't forget this step!

## TO RUN:

`uvicorn main:app --reload`
