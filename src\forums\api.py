from fastapi import APIRouter, Request
from src.forums.constants import FORUMS_LIST
from src.messages.functions import format_feed_group_message

forums = APIRouter()


@forums.get("s/list", response_description="Get all forums")
async def get_forum_categories(request: Request):
    return {"results": FORUMS_LIST}


@forums.get("s/feed", response_description="Get most recent 10 posts on all forums")
async def get_most_recent_forum_posts(request: Request, limit: int = 10):
    full_query = request.app.mongodb["messages"].aggregate(
        [
            {
                "$group": {
                    "_id": "$forum_id",
                    "messages": {
                        "$topN": {
                            "n": limit,
                            "sortBy": {"date_created": 1},
                            "output": "$$ROOT",
                        }
                    },
                }
            }
        ]
    )
    results = [
        format_feed_group_message(top_ten_forum_msgs)
        async for top_ten_forum_msgs in full_query
    ]

    # print('results: ' + str(results))
    return {"results": results}
