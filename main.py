import boto3
from fastapi import <PERSON><PERSON><PERSON>

# caching
from redis import asyncio as aioredis3
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend

# env
import os
from src.globals import AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, IS_DEV_ENV
from dotenv import load_dotenv

# middleware
from fastapi.middleware.cors import CORSMiddleware

# routers
from src.ai_assistants.api import ai_assistants
from src.users.api import users
from src.media.api.img_api import media_img_router
from src.messages.api import messages

# from src.messages.threads_api import threads
# from src.forums.api import forums
from src.auth.api import auth_router
from contextlib import asynccontextmanager

# db stuff
from src.api.db_api import db_router
from src.db.db_wrapper import get_db_url
from motor.motor_asyncio import AsyncIOMotorClient

if IS_DEV_ENV:
    load_dotenv()
else:
    AMAZON_RESOURCE_NAME = os.environ.get("AMAZON_RESOURCE_NAME")
    AWS_ACCESS_POINT_ALIAS = os.environ.get("AWS_ACCESS_POINT_ALIAS")
    AWS_S3_BUCKET_NAME = os.environ.get("AWS_S3_BUCKET_NAME")
    AWS_S3_REGION = os.environ.get("AWS_S3_REGION")

    OPENAI_ORG_ID = os.environ.get("OPENAI_ORG_ID")
    OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")

    OPENAI_CHLOE_ASSISTANT_ID = os.environ.get("OPENAI_CHLOE_ASSISTANT_ID")
    OPENAI_CHLOE_STORE_ID = os.environ.get("OPENAI_CHLOE_STORE_ID")

    OPENAI_KATIE_ASSISTANT_ID = os.environ.get("OPENAI_KATIE_ASSISTANT_ID")
    OPENAI_KATIE_STORE_ID = os.environ.get("OPENAI_KATIE_STORE_ID")

    DB_USERNAME = os.environ.get("DB_USERNAME")
    DB_PASSWORD = os.environ.get("DB_PASSWORD")
    DB_URI = os.environ.get("DB_URI")
    DB_NAME = os.environ.get("DB_NAME")

    TEST_EMAIL = os.environ.get("TEST_EMAIL")
    TEST_PASSWORD = os.environ.get("TEST_PASSWORD")
REDIS_URL = os.environ.get("REDIS_URL")

REDIS_URL = os.environ.get("REDIS_URL")

db_name = os.environ["DB_NAME"]


# Set up AWS S3 image bucket


@asynccontextmanager
async def lifespan(app: FastAPI):
    # MONGODB
    app.mongodb_client = AsyncIOMotorClient(get_db_url())
    app.mongodb = app.mongodb_client[db_name]

    # AWS BUCKET
    app.s3_client = boto3.client(
        "s3",
        aws_access_key_id=AWS_ACCESS_KEY_ID,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    )

    # REDIS
    redis = aioredis3.from_url(REDIS_URL, encoding="utf8", decode_responses=False)

    FastAPICache.init(RedisBackend(redis), prefix="fastapi-cache")
    yield
    # Clean up the mongodb client and release the resources
    app.mongodb_client.close()
    app.s3_client.close()


app = FastAPI(lifespan=lifespan)

if IS_DEV_ENV:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_origin_regex=".*",
        allow_methods=["*"],
        allow_headers=["*"],
    )

else:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[
            "https://reefdojo.com",
            "https://www.reefdojo.com",
            "https://api.reefdojo.com",
        ],
        allow_methods=["*"],
        allow_headers=["*"],
        allow_credentials=True,
    )


app.include_router(ai_assistants, prefix="/assistants", tags=["assistants"])
app.include_router(users, prefix="/user", tags=["users"])

# TODO: DELETE WHOLE FORUMS MODULE!
# app.include_router(forums, prefix="/forum", tags=["forums"])
app.include_router(messages, prefix="/message", tags=["messages"])
app.include_router(media_img_router, prefix="/media", tags=["media"])

# TODO: DELETE WHOLE THREADS MODULE!
# app.include_router(threads, prefix='/thread', tags=["messages"])
app.include_router(auth_router, prefix="/auth", tags=["auth"])
app.include_router(db_router, prefix="/db", tags=["db"])


@app.get("/")
async def root():
    return {"message": "Hello World"}
